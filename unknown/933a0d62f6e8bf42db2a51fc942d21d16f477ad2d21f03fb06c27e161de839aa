import logging
import json
from datetime import datetime

from flask import Blueprint, request, jsonify, current_app

from src.api.app.tracing import tracer
from src.api.app.auth.utils.auth_wrappers import verify_origin
from src.api.app.shared.processar_mensagem_recebida import processar_e_rotear_mensagem
from src.data.bigquery_data import get_from_instance
from src.extras.util import RoutesTracing

logger = logging.getLogger("conversas_logger")

receber_mensagem_bp = Blueprint('receber_mensagem', __name__)


@receber_mensagem_bp.route('', methods=['POST'])
@verify_origin()
@RoutesTracing(
    span_name_prefix="receber_mensagem",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def receber_mensagem(origin="z_api"):
    try:
        momento_recebimento = datetime.now().timestamp()
        logger.info("\n\n\nEntrou no receber mensagem\n\n\n")
        data = request.json

        id_empresa = data.get('chave_empresa', None)

        if not data:
            return jsonify({"error": "No data received"}), 400

        id_empresa = data.get('chave_empresa', None)

        logger.info(f"request: {json.dumps(request.json, indent=2)}")


        if origin == "z_api":
            id_empresa, _ = get_from_instance(data["instanceId"])

        elif origin == "gym_bot":
            id_empresa = request.args["id_empresa"]

        if data.get("messageId", None) == "integration-test":
            id_empresa = request.args["id_empresa"] 

        # Task inicial (mínima)
        task_inicial = {
            "id_empresa": id_empresa,
            "data": data,
            "sessionId": None,
            "canAnswer": True,
            "origin": origin,
            "momento_recebimento": momento_recebimento
        }

        response_data, status_code = processar_e_rotear_mensagem(
            task_inicial=task_inicial,
            redis_client=current_app.redis_client,
            logger=logger,
            tracer=tracer
        )

        return jsonify(response_data), status_code

    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
