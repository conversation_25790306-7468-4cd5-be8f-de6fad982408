from datetime import datetime
from src.extras.util import parse_phone

from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.data.bigquery_data import get_from_telefone, get_from_empresa

import logging

logger = logging.getLogger("conversas_logger")


class TaskAdapter():
    def __init__(self, task, origin, id_empresa):
        logger.info(f"[TASK ADAPTER] Origin: {origin},\tID Empresa: {id_empresa}")
        self.messager_api = origin
        self.id_empresa = id_empresa
        if self.messager_api == "gym_bot":
            logger.info("[TASK ADAPTER] Transformando task para GymBot")
            self._new_task = self.gym_bot_sem_fluxo(task)
        elif self.messager_api == "z_api":
            logger.info("[TASK ADAPTER] Transformando task para z_api")
            self._new_task = self.zapi(task)

    @property
    def new_task(self) -> dict | None:
        return self._new_task
    
    def zapi(self, task):
        data = task.get("data", {})
        is_from_zapi = data.get("fromMe", False)
        is_sent = data.get("status", "RECEIVED") == "SENT"
        if is_from_zapi or is_sent:
            return None
        new_task = task.copy()
        new_task["data"]["sessionId"] = None
        new_task["canAnswer"] = True
        new_task["data"]["departamento"] = None
        new_task["data"]["colaborador"] = None
        return new_task, "success"

    def gym_bot_sem_fluxo(self, task):
        logger.info(f"[TASK ADAPTER] SEM FLUXO")
        gbit = GymbotIntegrationTools(self.id_empresa)
        session_id = task["data"]["content"]["sessionId"]
        mensagem_id = task["data"]["content"]["id"]
        instance_id, _ = get_from_empresa(self.id_empresa)
        if not instance_id:
            return {}, f'instance_id não encontrado para empresa: {self.id_empresa}'
        
        conversa = gbit.get_conversa(session_id, includeDetails=["ChannelDetails", "ContactDetails", "DepartmentsDetails", "agentDetails"])
        logger.info(f"\n\n[TASK ADAPTER] Conversa: {conversa}")
        if conversa and conversa.get('error', False):
            return {}, f'Conversa não encontrada para session_id: {session_id}'
        
        canal = conversa.get('channelDetails', None)
        if canal is None:
            return {}, f'Detalhes do canal de atendimento não encontrado. session_id {session_id}'
        telefone_empresa = parse_phone(canal["humanId"], origin=self.messager_api)
        logger.info(f"\n\n[TASK ADAPTER] Canal: {canal}")
        logger.info(f"\n\n[TASK ADAPTER] telefone_empresa: {telefone_empresa}")

        contato = conversa.get('contactDetails', None)
        if contato is None:
            return {}, f'Detalhes do contato não encontrado. session_id {session_id}'
        telefone_usuario = parse_phone(contato["phonenumber"], origin=self.messager_api)
        logger.info(f"\n\n[TASK ADAPTER] Contato: {contato}")
        logger.info(f"\n\n[TASK ADAPTER] telefone_usuario: {telefone_usuario}")

        departament = conversa.get("departmentDetails", None)
        if departament is None:
            return {}, f'Detalhes do departamento não encontrado. session_id {session_id}'
        logger.info(f"\n\n[TASK ADAPTER] Departamento: {departament}")

        colaborador = conversa.get("agentDetails") if conversa.get("agentDetails") else {}
        logger.info(f"\n\n[TASK ADAPTER] Colaborador: {colaborador}")

        msg_date = task["data"]["content"]["createdAt"].split(".")[0]
        new_task = task.copy()
        new_task["canAnswer"] = (departament["name"] == "ConversasAI")
        new_task["data"] = {
            "instanceId": instance_id, 
            "connectedPhone": telefone_empresa,
            "phone": telefone_usuario,
            "isGroup": False,
            "participantPhone": None,
            "messageId": mensagem_id,
            "chave_empresa": None,
            "telefone": None,
            "mensagem": None,
            "momment": datetime.timestamp(
                datetime.strptime(
                    msg_date, 
                    "%Y-%m-%dT%H:%M:%S"
                )
            )*1000,
            "sessionId": session_id,
            "departamento": departament["name"],
            "colaborador": colaborador.get("name", None),
        }
        

        menssage_type = task["data"]["content"]["type"]
        # new_task["data"]["messageType"] = menssage_type.lower()
        if menssage_type in ["TEXT", "LIST"]:
            new_task["data"]["text"] = {
                "message": task["data"]["content"]["text"]
            }
        else:
            mimetype = task["data"]["content"]["details"]["file"]["mimeType"]
            extension = task["data"]["content"]["details"]["file"]["extension"].replace(".", "")
            new_task["data"][menssage_type.lower()] = {
                f"{menssage_type.lower()}Url": task["data"]["content"]["details"]["file"]["publicUrl"],
                "mimeType": f"{mimetype}; codecs={extension}",
            }

        return new_task, "success"
    
    def gym_bot_com_fluxo(self, task):
        logger.info(f"[TASK ADAPTER] COM FLUXO")
        gbit = GymbotIntegrationTools(self.id_empresa)

        canais = gbit.get_canais_atendimento()
        logger.info(f"[TASK ADAPTER]\n\nCanais: {canais}\n\n")
        telefone_empresa = parse_phone(canais[0]["number"])
        logger.info(f"[TASK ADAPTER]\n\ntelefone_empresa: {telefone_empresa}\n\n")

        empresa_id = get_from_telefone(telefone_empresa)
        logger.info(f"[TASK ADAPTER]\nempresa_id: {empresa_id}\n\n")
        instance_id, _ = get_from_empresa(empresa_id)
        logger.info(f"[TASK ADAPTER]\ninstance_id: {instance_id}\n\n")


        msg_date = task["data"]["lastMessage"]["createdAt"].split(".")[0]
        new_task = {
            "id_empresa": empresa_id,
            "data": {
                "instanceId": instance_id,
                "connectedPhone": task["data"]["channel"]["key"],
                "phone": parse_phone(task["data"]["contact"]["phonenumber"]),
                "isGroup": False,
                "participantPhone": None,
                "messageId": task["data"]["lastMessage"]["id"],
                "chave_empresa": None,
                "telefone": None,
                "mensagem": None,
                "momment": datetime.timestamp(
                    datetime.strptime(
                        msg_date, 
                        "%Y-%m-%dT%H:%M:%S"
                    )
                )*1000,
                "sessionId": task["data"]["sessionId"]
            }
        }

        menssage_type = task["data"]["lastMessage"]["type"]
        if menssage_type == "TEXT":
            new_task["data"]["text"] = {
                "message": task["data"]["lastContactMessage"]
            }
        else: 
            mimetype = task["data"]["lastMessage"]["file"]["mimeType"]
            extension = task["data"]["lastMessage"]["file"]["extension"].replace(".", "")
            new_task["data"][menssage_type.lower()] = {
                f"{menssage_type.lower()}Url": task["data"]["lastMessage"]["file"]["publicUrl"],
                "mimeType": f"{mimetype}; codecs={extension}",
            }
         
        
        return new_task, "syccess"
