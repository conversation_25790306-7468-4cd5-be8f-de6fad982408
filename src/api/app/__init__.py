from flasgger import Swagger
from flask import Flask
from flask_login import (
    login_required,
    LoginManager
)
from flask_redis import FlaskRedis
import logging

from src.api.app.routes.rede import rede_bp
from src.api.app.routes.index import index_bp
from src.api.app.routes.v2 import (
    empresa_bp,
    sticker_bp,
    doc_bp,
    users_bp,
    bi_bp,
    config_bp,
    schedule_bp
)
from src.api.app.routes.atualizar_contexto import (
    atualizar_contexto_academia_bp,
    atualizar_contexto_planos_bp,
    atualizar_contexto_fases_bp,
    atualizar_contexto_turmas_bp,
    atualizar_contexto_personalidade_bp,
    atualizar_dados_z_api_bp,
    atualizar_contexto_produtos_bp,
    atualizar_contexto_usuario_bp,
    atualizar_messager_channel_bp,
    atualizar_contexto_campanha_bp
)
from src.api.app.routes.consultar_contexto import (
    consultar_contexto_academia_bp,
    consultar_contexto_planos_bp,
    consultar_contexto_fases_bp,
    consultar_contexto_turmas_bp,
    consultar_contexto_personalidade_bp,
    consultar_contexto_aluno_bp,
    consultar_contexto_produtos_bp,
    consultar_contexto_campanhas_bp
)
from src.api.app.routes.enviar_mensagem import enviar_mensagem_bp
from src.api.app.routes.apagar_contexto import apagar_contexto_academia_bp, apagar_contexto_planos_bp, \
    apagar_contexto_fases_bp, apagar_contexto_turmas_bp, apagar_contexto_personalidade_bp, apagar_contexto_aluno_bp, \
    apagar_strikes_aluno_bp, apagar_contexto_produtos_bp, apagar_contexto_campanha_bp
from src.api.app.routes.gym_bot import gymbot_webhook_bp, atualizar_token_bp, departamento_gymbot_bp, consultar_token_bp, departamento_instrucoes_bp, departamento_bp
from src.api.app.routes.health import health_bp
from src.api.app.routes.receber_mensagem import receber_mensagem_bp
from src.api.app.auth.auth import authorization_bp, registration_bp, unregistration_bp, get_api_keys_bp
from src.api.app.routes.error_handler import handle_error
from src.api.app.routes.z_api_routes.z_api_routes import (
    get_qr_code_bp,
    disconnect_instance_bp,
    check_instance_status_bp,
    create_instance_bp,
    subscribe_instance_bp,
    cancel_instance_bp,
    get_instance_bp,
    profile_bp,
    device_bp,
    get_token_from_instance_bp
)
from src.api.app.routes.scheduler import schedule_contact_bp, notification_scheme_bp, available_events_bp
from src.api.app.routes.status_mensagem import status_mensagem_bp
from src.api.app.limiter.limiter import base_limiter
from src.api.app.auth.google_oauth import google_login_bp, callback_bp
from src.api.app.models.user import User
from src.api.app.before_request.before_request import before_request_routine

logger = logging.getLogger("conversas_logger")


def create_app():
    app = Flask(__name__)
    app.config.from_object("src.extras.config.Config")

    app.register_blueprint(index_bp, url_prefix='/')

    app.register_blueprint(empresa_bp, url_prefix='/empresa')
    app.register_blueprint(sticker_bp, url_prefix='/sticker')
    app.register_blueprint(doc_bp, url_prefix='/doc')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(bi_bp, url_prefix='/bi')
    app.register_blueprint(config_bp, url_prefix='/configuracao')
    app.register_blueprint(schedule_bp, url_prefix='/v2/scheduler')

    app.register_blueprint(enviar_mensagem_bp, url_prefix='/enviar_mensagem/')
    app.register_blueprint(atualizar_contexto_academia_bp, url_prefix='/atualizar_contexto_academia/')
    app.register_blueprint(atualizar_contexto_planos_bp, url_prefix='/atualizar_contexto_planos/')
    app.register_blueprint(atualizar_contexto_fases_bp, url_prefix='/atualizar_contexto_fases/')
    app.register_blueprint(atualizar_contexto_turmas_bp, url_prefix='/atualizar_contexto_turmas/')
    app.register_blueprint(atualizar_contexto_personalidade_bp, url_prefix='/atualizar_contexto_personalidade/')
    app.register_blueprint(atualizar_contexto_produtos_bp, url_prefix='/atualizar_contexto_produtos/')
    app.register_blueprint(atualizar_contexto_usuario_bp, url_prefix='/atualizar_contexto_usuario')
    app.register_blueprint(atualizar_dados_z_api_bp, url_prefix='/atualizar/dados_z_api/')
    app.register_blueprint(atualizar_contexto_campanha_bp, url_prefix='/atualizar_contexto_campanha/')
    app.register_blueprint(consultar_contexto_academia_bp, url_prefix='/consultar_contexto_academia/')
    app.register_blueprint(consultar_contexto_planos_bp, url_prefix='/consultar_contexto_planos/')
    app.register_blueprint(consultar_contexto_fases_bp, url_prefix='/consultar_contexto_fases/')
    app.register_blueprint(consultar_contexto_turmas_bp, url_prefix='/consultar_contexto_turmas/')
    app.register_blueprint(consultar_contexto_personalidade_bp, url_prefix='/consultar_contexto_personalidade/')
    app.register_blueprint(consultar_contexto_aluno_bp, url_prefix='/consultar_contexto_aluno/')
    app.register_blueprint(consultar_contexto_produtos_bp, url_prefix='/consultar_contexto_produtos/')
    app.register_blueprint(consultar_contexto_campanhas_bp, url_prefix='/consultar_contexto_campanhas/')
    app.register_blueprint(apagar_contexto_academia_bp, url_prefix='/apagar_contexto_academia/')
    app.register_blueprint(apagar_contexto_planos_bp, url_prefix='/apagar_contexto_planos/')
    app.register_blueprint(apagar_contexto_fases_bp, url_prefix='/apagar_contexto_fases/')
    app.register_blueprint(apagar_contexto_turmas_bp, url_prefix='/apagar_contexto_turmas/')
    app.register_blueprint(apagar_contexto_personalidade_bp, url_prefix='/apagar_contexto_personalidade/')
    app.register_blueprint(apagar_contexto_aluno_bp, url_prefix='/apagar_contexto_aluno/')
    app.register_blueprint(apagar_contexto_produtos_bp, url_prefix='/apagar_contexto_produtos/')
    app.register_blueprint(apagar_strikes_aluno_bp, url_prefix='/apagar_strikes_aluno/')
    app.register_blueprint(apagar_contexto_campanha_bp, url_prefix='/apagar_contexto_campanha/')
    app.register_blueprint(receber_mensagem_bp, url_prefix='/receber_mensagem/')
    app.register_blueprint(authorization_bp, url_prefix='/auth/')
    app.register_blueprint(registration_bp, url_prefix='/register/')
    app.register_blueprint(unregistration_bp, url_prefix='/unregister/')
    app.register_blueprint(get_api_keys_bp, url_prefix='/api/keys/')
    app.register_blueprint(health_bp, url_prefix='/health/')

    app.register_blueprint(get_qr_code_bp, url_prefix='/get_qr_code')
    app.register_blueprint(disconnect_instance_bp, url_prefix='/disconnect_instance')
    app.register_blueprint(check_instance_status_bp, url_prefix='/check_instance_status')
    app.register_blueprint(create_instance_bp, url_prefix='/create_instance')
    app.register_blueprint(subscribe_instance_bp, url_prefix='/subscribe_instance')
    app.register_blueprint(cancel_instance_bp, url_prefix='/cancel_instance')
    app.register_blueprint(get_instance_bp, url_prefix='/instance')
    app.register_blueprint(profile_bp, url_prefix='/profile')
    app.register_blueprint(device_bp, url_prefix='/device')
    app.register_blueprint(atualizar_messager_channel_bp, url_prefix="/atualizar_messager_channel_bp")

    app.register_blueprint(google_login_bp, url_prefix='/login')
    app.register_blueprint(callback_bp, url_prefix='/login/callback')

    app.register_blueprint(status_mensagem_bp, url_prefix='/status_mensagem')

    app.register_blueprint(gymbot_webhook_bp, url_prefix='/gymbot_webhook')
    app.register_blueprint(atualizar_token_bp, url_prefix='/atualizar_token')
    app.register_blueprint(consultar_token_bp, url_prefix='/consultar_token')
    app.register_blueprint(departamento_bp, url_prefix='/departamento')
    app.register_blueprint(departamento_gymbot_bp, url_prefix='/departamento_gymbot')
    app.register_blueprint(get_token_from_instance_bp, url_prefix='/get_token_from_instance')
    app.register_blueprint(departamento_instrucoes_bp, url_prefix='/departamento_instrucoes')

    app.register_blueprint(schedule_contact_bp, url_prefix='/schedule_contact')
    app.register_blueprint(notification_scheme_bp, url_prefix='/notification_scheme')
    app.register_blueprint(available_events_bp, url_prefix='/available_events')

    app.register_blueprint(rede_bp, url_prefix='/rede')

    redis_client = FlaskRedis(app)
    app.redis_client = redis_client

    login_manager = LoginManager()
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.get(int(user_id))

    base_limiter.init_app(app)

    swagger_config = {
        "info": {
            "title": "API - Conversas.AI",
            "description": "Essa API serve para gerenciar os dados, e ações da aplicação Conversas.AI.",
            "version": "1.0",
        },
        "securityDefinitions": {
            "Bearer": {
                "type": "apiKey",
                "name": "Authorization",
                "in": "header",
                "description": "\
                JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\""
            }
        },
        "security": [
            {
                "Bearer": []
            }
        ]
    }

    if app.config["ENV"] == "production" and app.config["GOOGLE_LOGIN_DOC"] == "true":
        swagger = Swagger(app, template=swagger_config, decorators=[login_required])
    else:
        swagger = Swagger(app, template=swagger_config)

    app.errorhandler(Exception)(handle_error)

    app.before_request(before_request_routine)

    return app
