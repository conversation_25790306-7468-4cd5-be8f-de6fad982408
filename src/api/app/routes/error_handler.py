from flask import jsonify, request, redirect, url_for
from werkzeug.exceptions import UnsupportedMediaType, NotFound
from werkzeug.exceptions import Unauthorized as WUnauthorized
import traceback
import logging

from src.api.app.exception.exception import BadRequest, Forbidden, Unauthorized

logger = logging.getLogger("conversas_logger")

def handle_error(e):
    code = e.code if hasattr(e, 'code') else 500
    response = {
        "error": "internal server error",
    }
    error_message = e.description if hasattr(e, 'description') else None
    if error_message:
        response["error"] = error_message
    custom_error = None
    if isinstance(e, BadRequest):
        custom_error = e.to_dict()
    elif isinstance(e, Unauthorized):
        custom_error = e.to_dict()
    elif isinstance(e, Forbidden):
        custom_error = e.to_dict()
    elif isinstance(e, WUnauthorized):
        if 'apidocs' in str(request.url):
            return redirect(url_for('google_login.google_login', next='apidocs'))
    elif isinstance(e, UnsupportedMediaType):
        response = {
            "error": "unsupported media type",
        }
    elif isinstance(e, NotFound):
        response = {
            "error": "not found",
        }
    if custom_error:
        response = {"error": custom_error["error"]}
        code = custom_error["status_code"]
    traceback_=str(traceback.format_exc())
    #logger.info(f"Error: {error}")
    logger.info(f"Traceback: {traceback_}")
#    logger.info(f"""
#REQUEST INFO:
#- Method:\n {request.method}
#- URL:\n {request.url}
#- Headers:\n {request.headers}
#- Body:\n {request.data}
#- Origin:\n {request.origin}
#- Remote Address:\n {request.remote_addr}
#    """)
    return jsonify(response), code
