import requests as rq
from src.extras.config import Config
from src.extras.test.mock_data import MockData
from src.data.bigquery_data import get_from_empresa
from src.extras.util import retry
import os
import logging
from abc import ABC, abstractmethod
import base64
from src.data.google_storage import Bucket
from src.extras.util import register_log
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools as GBIT
import json
from src.data.bigquery_data import BigQueryData as bq
from src.connections.connections import Connections

logger = logging.getLogger("conversas_logger")
BUCKET_NAME_AUDIO = os.getenv("BUCKET_NAME_AUDIO", "temporary_audios")
GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET", "development")
if GCP_BIGQUERY_DATASET == "development":
    BUCKET_NAME_AUDIO += "_dev"

class MessengerAdapter(ABC):
    
    @abstractmethod
    def send_message(self, *args, **kwargs):
        pass

    @abstractmethod
    def receive_message(self, *args, **kwargs):
        pass
    
    @abstractmethod
    def end_conversation(self, *args, **kwargs):
        pass
    
class GymBot(MessengerAdapter):
    def __init__(self, id_empresa) -> None:
        # esse token exposto aqui é temporario, tem que adicionar nas variáveis de ambiente
        self.bq_ = bq(id_empresa=id_empresa)
        self.id_session = None  # Will be populated after sending a message
        self.token = self.bq_.get_gymbot_token()

    def send_message(self, message_data, phone, connected_phone, id_empresa, message_type="text", message_id=None, **kwargs):
        url = f"https://api.wts.chat/chat/v1/message/send-sync"
        
        requestbody = {"body": {}}
        if message_type == "text":
            requestbody["body"] = {"text": message_data} 

        elif message_type == "audio":
            bucket = Bucket(BUCKET_NAME_AUDIO)
            blob = bucket.bucket.blob(f"{phone}/{message_id}.mp3")
            blob.upload_from_string(message_data, content_type="audio/mpeg")
            blob.make_public()
            file_url = blob.public_url

            requestbody["body"] = {"fileUrl": file_url}

        elif message_type == "image":
            file_url = kwargs.get('imagem')
            requestbody["body"] = {"fileUrl": file_url, "text": message_data}
            
        requestbody['from'] = connected_phone
        requestbody['to'] = phone
        logger.info(f"[GYMBOT] body: {json.dumps(requestbody, indent=2, ensure_ascii=False)}")
        
        #logger.info(f"\n\nmessage_data: {message_data}\n\n")
        headers = {
            'Authorization': f'Bearer {self.token}',
            'accept': 'application/json',
            'Content-Type': 'application/*+json'
        }

        gbit = GBIT(id_empresa)
        status, response = gbit.enviar_mensagem(requestbody, headers)
        
        logger.info(f"Response status code: {status}")
        logger.info(f"Response: {json.dumps(response, indent=2, ensure_ascii=False)}")
        self.id_session = response.get("sessionId")
        return response.get('id', None)
    
    def receive_message(self):
        pass
    
    def end_conversation(self, *args, **kwargs):
        pass

BOT_CONVERSA_API = "https://backend.botconversa.com.br/api/v1/webhook/"

class BotConversa(MessengerAdapter):
    def __init__(self) -> None:
        self.api_key = os.getenv('BOT_CONVERSA_API_KEY')

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def create_subscriber(self, phone, first_name, last_name, empresa) -> dict:
        if Config().isLoadTesting():
            return MockData.get_whatsapp_user_info()
        
        phone = phone.replace(" ", "").replace("(", "").replace(")", "").replace("-", "")
        phone = phone.replace("+", "")
        has_phone, subscriber = self.get_subscriber_by_phone(phone)
        if has_phone:
            logger.info("Telefone já conhecido. Retornando subscriber existente.")
            self.set_value_to_custom_field(subscriber['id'], "Empresa", empresa)
            return subscriber
        logger.info("Criando novo subscriber no BotConversa...")
        
        url = f"{BOT_CONVERSA_API}subscriber/"

        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }

        data = {
            "phone": phone,
            "first_name": first_name,
            "last_name": last_name
        }

        response = rq.post(url, headers=headers, json=data)

        user_info = response.json()
        self.set_value_to_custom_field(user_info['id'], "Empresa", empresa)
        
        try:
            return response.json()

        except:
            return response

    def delete_subscriber(self, id_botconversa) -> dict:
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/delete/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        response = rq.delete(url, headers=headers)
        try:
            return response.json()

        except:
            return response

    def get_subscribers(self) -> dict:
        url = f"{BOT_CONVERSA_API}subscribers/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key
        }
        response = rq.get(url, headers=headers)
        try:
            return response.json()['results']

        except:
            return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def get_subscriber_by_phone(self, phone) -> tuple[bool, dict]:

        if Config().isLoadTesting():
            return True, MockData.get_whatsapp_user_info()

        phone = phone.replace(" ", "").replace("(", "").replace(")", "").replace("-", "").replace("+", "")
        logger.info(f"Buscando usuário com telefone {phone} no BotConversa...")
        url = f"{BOT_CONVERSA_API}subscriber/get_by_phone/{phone}/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key
        }
        response = rq.get(url, headers=headers)
        if response.status_code == 404:
            match len(phone): 
                case 13:
                    prefix = phone[:4]
                    suffix = phone[5:]
                    phone = f"{prefix}{suffix}"
                    url = f"{BOT_CONVERSA_API}subscriber/get_by_phone/{phone}/"
                    response = rq.get(url, headers=headers)
                case 12:
                    prefix = phone[:4]
                    suffix = phone[4:]
                    phone = f"{prefix}9{suffix}"
                    url = f"{BOT_CONVERSA_API}subscriber/get_by_phone/{phone}/"
                    response = rq.get(url, headers=headers)
            if response.status_code == 404:
                return False, response
        return True, response.json()
    
    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def get_flows(self):
        url = f"{BOT_CONVERSA_API}flows/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key
        }
        response = rq.get(url, headers=headers)
        try:
            return response.json()
    
        except:
            return response

    def get_flow_by_name(self, flow_name):
        flows = self.get_flows()
        for flow in flows:
            if flow['name'] == flow_name:
                return flow

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def get_custom_fields(self):
        url = f"{BOT_CONVERSA_API}custom_fields/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key
        }
        response = rq.get(url, headers=headers)
        try:
            return response.json()
    
        except:
            return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def get_custom_field_by_name(self, field_name):
        fields = self.get_custom_fields()
        for field in fields:
            if field['key'] == field_name:
                return field

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def clear_custom_field(self, id_botconversa, field_id):
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/custom_fields/{field_id}/"
        
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key
        }

        try:
            response = rq.delete(url, headers=headers)
        except:
            return {"message": "Error clearing custom field."}
        
        return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def set_value_to_custom_field(self, id_botconversa, field_name, value):
        logger.info(F"Setando valor {value} para o campo personalizado {field_name} do usuário {id_botconversa}.")
        
        field_id = self.get_custom_field_by_name(field_name)['id']
        self.clear_custom_field(id_botconversa, field_id)

        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/custom_fields/{field_id}/"
        
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "value": value
        }
        response = rq.post(url, headers=headers, json=data)
        
        try:
            return response.json()
    
        except:
            return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def send_conversation_flow(self, flow_name, id_botconversa):
        if Config().isLoadTesting():
            return {"message": "Load testing. No WhatsApp flow sent."}

        logger.info(f"Enviando flow {flow_name} para o usuário {id_botconversa}.")

        flow = self.get_flow_by_name(flow_name)
        flow_id = flow['id']
        
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/send_flow/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "flow": flow_id
        }
        response = rq.post(url, headers=headers, json=data)

        try:
            return response.json()

        except:
            return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def open_conversation(self, manager, id_botconversa) -> dict:
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/change_conversation_status/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "open_conversation": True,
            "manager": manager
        }
        response = rq.post(url, headers=headers, json=data)
        try:
            return response.json()

        except:
            return response

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def close_conversation(self, manager, id_botconversa) -> dict:
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/change_conversation_status/"
        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "open_conversation": False,
            "manager": manager
        }
        response = rq.post(url, headers=headers, json=data)
        try:
            return response.json()

        except:
            return response
        
    def end_conversation(self, flow_name, phone, first_name, last_name, id_empresa, **kwargs):
        user_info = self.create_subscriber(phone=phone, first_name=first_name, last_name=last_name, empresa=id_empresa)
        
        id_botconversa = user_info['id']

        self.send_conversation_flow(flow_name, id_botconversa)
        return None
    
    @retry(retries=3, delay=2, backoff=2, status_codes=(500,))
    def send_message(self, phone=None, first_name=None, last_name=None, id_empresa=None, flow=None, message_data=None, message_type='text', **kwargs):
        if Config().isLoadTesting():
            return {"message": "Load testing. No WhatsApp message sent."}
        phone = str(phone)
        user_info = self.create_subscriber(phone=phone, first_name=first_name, last_name=last_name, empresa=id_empresa)
        
        id_botconversa = user_info['id']
        logger.info(f"Usuário {id_botconversa} criado no BotConversa.")
        
        self.send_conversation_flow(flow, id_botconversa)
        
        logger.info(f"Enviando mensagem para o usuário {id_botconversa}.")
        url = f"{BOT_CONVERSA_API}subscriber/{id_botconversa}/send_message/"

        headers = {
            "accept": "application/json",
            "API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "type": message_type,
            "value": message_data
        }

        response = rq.post(url, headers=headers, json=data)

        try:
            return response.json()

        except:
            return response

    def receive_message(self):
        # Implementação específica para BotConversa
        pass


class ZApi(MessengerAdapter):
    def __init__(self) -> None:
        self.client_token = os.getenv('Z_API_CLIENT_TOKEN')

    @retry(retries=3, delay=2, backoff=2, status_codes=(500,400))
    def send_message(self, message_data, phone, id_empresa, message_id=None, message_type="text", is_group=False, **kwargs):
        # Implementação específica para ZApi
        # O método utiliza apenas os parâmetros necessários e ignora os outros
        #logger.info(f"Enviando mensagem pelo ZApi: {message_data} para {phone}")
        
        instance_id, instance_token = get_from_empresa(id_empresa)
        file_url = kwargs.get('imagem')
        message_type = "text" if message_type == "image" and not file_url else message_type

        if message_type == "text":
            requestbody = {
                "phone": phone,
                "message": message_data
            }
        elif message_type == "audio":
            requestbody = {
                "phone": phone,
                message_type: f"data:audio/mpeg;base64,{base64.b64encode(message_data).decode('ascii')}"
            }
        elif message_type == "image":
            requestbody = {
                "phone": phone,
                message_type: file_url,
                "caption": message_data
            }

        if message_id and is_group:
            requestbody["messageId"] = message_id
        
        url = f'https://api.z-api.io/instances/{instance_id}/token/{instance_token}/'
        logger.info(url)
        response = rq.post(url+f"send-{message_type}", json=requestbody, headers={'Content-Type': 'application/json',
                                                    'client-token': self.client_token})
        
        logger.info(f"status mensagem enviada: {response.status_code}")
        logger.info(f"content mensagem enviada: {response.content}")
        try:
            return response.json().get('messageId', None)

        except:
            return response
        
    def receive_message(self):
        # Implementação específica para ZApi
        pass
    
    def end_conversation(self, phone, id_empresa, sticker: str = None, **kwargs):
        if sticker is None:
            return None
        instance_id, instance_token = get_from_empresa(id_empresa)
        url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}/send-sticker"
        requestbody = {
            "phone": phone,
            "sticker": sticker
        }
        response = rq.post(url, json=requestbody, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
            })
        
        logger.info(response.content)
        try:
            return response.json().get('messageId', None)
        
        except:
            return response


class AutoTestsMessager(MessengerAdapter):
    def __init__(self, webhook_url: str, test_name: str) -> None:
        self.webhook_url = webhook_url
        self.test_name = test_name

    @retry(retries=3, delay=2, backoff=2, status_codes=(500, 400))
    def send_message(
        self, message_data, phone,
        id_empresa, message_id=None, message_type="text",
        is_group=False, **kwargs
    ):

        if self.webhook_url is None and self.test_name is None:
            redis_client = Connections.get_instance().redis_client
            test_info = redis_client.get(f"tests:attributes_by_info:{id_empresa}:{phone}")
            if test_info:
                test_info = json.loads(test_info.decode("utf-8"))
                self.webhook_url = test_info.get("webhook_url")
                self.test_name = test_info.get("test_name")
            else:
                return {"message": "Mensagem não enviada!"}

        requestbody = {
            "phone": phone,
            "message": message_data,
            "message_type": message_type,
            "is_group": is_group,
            "message_id": message_id
        }
        params = {
            "test_name": self.test_name
        }
        logger.info(f"Sending message to {self.webhook_url}/message?test_name={self.test_name}")
        response = rq.post(
            f"{self.webhook_url}/message",
            json=requestbody,
            params=params,
            timeout=10
        )
        logger.info(f"Response: {response.status_code} - {response.text}")
        return response.json()

    def receive_message(self):
        pass

    def end_conversation(self, *args, **kwargs):
        pass


class WhatsappMessager:
    def __init__(self, api_choice, **kwargs) -> None:
        if api_choice == 'z_api':
            self.messenger = ZApi()
        elif api_choice == 'bot_conversa':
            self.messenger = BotConversa()
        elif api_choice == 'gym_bot':
            id_empresa = kwargs['id_empresa']
            self.messenger = GymBot(id_empresa)
        elif api_choice == 'auto_tests':
            self.messenger = AutoTestsMessager(**kwargs['test_args'])
        self._provider = api_choice

    def send_message(self, *args, **kwargs):
        logger.info(f"Sending message with {self.messenger.__class__.__name__}")
        return self.messenger.send_message(*args, **kwargs)

    def receive_message(self, *args, **kwargs):
        return self.messenger.receive_message(*args, **kwargs)

    def end_conversation(self, *args, **kwargs):
        return self.messenger.end_conversation(*args, **kwargs)

    @property
    def provider(self):
        return self._provider
